@import "@styles/variables";
@import "@styles/mixins";

.greetings {
  // height: 800px;
  &-container {
    &__top-container {
      margin-bottom: 16px;
      display: flex;
      gap: 10px !important;
      align-items: center;
      justify-content: space-between;

      > div {
        display: flex;
        gap: 8px;
      }

      .select-container {
        width: 100%;
        background: $white;
        border: 1px solid #fff;

        &-reg {
          width: 148px;
        }
      }
    }

    .occasion-buttons {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 18px; /* 128.571% */
      letter-spacing: -0.14px;
      color: $dark-purple;
      padding: 4px 12px;
      border-radius: 8px;
      background: #f5f5f5;
    }

    .toggle-buttons {
      all: unset;
      border-radius: 8px;
      height: 26px;
      padding: 0 12px;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;

      @include font-size(14);
    }

    .on {
      background: $dark-purple;
      color: $white;
    }

    .off {
      background: $whitish;
      color: $dark-purple;
    }
  }
}

.select-menu-item {
  max-height: 200px !important;
  border-radius: 12px !important;
  font-size: 12px;
}

.empty-container {
  padding: 20px 8px;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  opacity: 0.75;
  color: #f00;
}

.error-container {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 5px;
  img {
    width: 18px;
    height: 18px;
  }
  p {
    margin: 0;
    font-weight: 600;
    @include font-size(14);
    color: $oops-red;
  }
}
.brand-button__continue {
  flex: 2;
}

.brand-greetings-accordion {
  overflow-y: scroll;
  max-height: calc(100vh - 267px);
  padding-bottom: 20px;
  scrollbar-width: none;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  min-height: 60px;
}

.confirm-modal {
  &__wrapper {
    padding: 20px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    max-height: calc(100vh - 131px);
  }
  &__header {
    position: sticky;

    h5 {
      margin: 16px 0;
      font-size: 24px;
      font-style: normal;
      font-weight: 800;
      line-height: 24px;
      font-family: $bricolage-font-family;
      font-optical-sizing: none;
    }
  }

  &__body {
    overflow-y: scroll;
    max-height: calc(100vh - 212px);
    scrollbar-width: none;
    p {
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
      letter-spacing: -0.16px;
      color: $dark-purple;
      padding: 16px 0;
      border-bottom: 0.5px solid #f5f5f5;
      cursor: pointer;
      transition:
        background-color 0.2s ease,
        color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }
    }
  }
  &__line {
    margin: 0 auto;
    width: 48px;
    height: 4px;
    background-color: #d9d9d9;
    border-radius: 12px;
  }
}

.upload-icon {
  position: absolute;
  top: 20%;
  z-index: 99;
  transform: translate(50%);
  right: 50%;
  @include rtl-styles {
    transform: unset;
  }
  .wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    font-weight: 600;
  }
}

.grid-slide {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  overflow-y: scroll;
  margin-bottom: 16px;
  position: relative;

  @include rtl-styles {
    direction: rtl;
  }

  &__greetings-preview {
    // width: 105px;
    // max-height: 147px;
    position: relative;
    border-radius: 12px;
    // border: 2px solid #0071ff;
    overflow: hidden;
    margin-top: 3px;
    margin-left: 3px;
    @include rtl-styles {
      margin-right: 3px;
      margin-left: 0;
    }
    cursor: pointer !important;
    .preview {
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      width: 100%;
      height: 100%;
      inset: 0;
    }
    &-content {
      position: absolute;
      z-index: 99;
      inset: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 7px;
      span {
        color: #fff;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 130%; /* 15.6px */
        letter-spacing: 0.12px;
      }
    }
  }

  .grid-slide-item {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    position: relative;
    height: fit-content;
  }

  .grid-slide-item img {
    display: block;
    width: 100%;
    border-radius: 12px;
    // border: 4px solid transparent;
  }

  .select-box {
    z-index: 98;
    position: absolute;
    background-color: #fff;
    width: 28px;
    height: 28px;
    top: -1px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .custome {
    right: unset !important;
    top: 2px;
    left: 63px !important;
    @include rtl-styles {
      right: 0 !important;
      left: unset !important;
    }
  }

  .active {
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, 12%);
    background: $white;
  }

  .hide {
    display: none;
  }
}

.select-box {
  z-index: 98;
  position: absolute;
  background-color: #fff;
  width: 28px;
  height: 28px;
  top: -1px;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 12px;
  // border-top-right-radius: 12px;
}

.custome {
  // right: unset !important;
  top: -2px;
  left: inherit;
  @include rtl-styles {
    right: inherit;
    // left: unset !important;
  }
}
