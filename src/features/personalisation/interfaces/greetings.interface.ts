export interface GreetingsOccasionInterface {
    occasions: {
        edges: Occasions[] | any;
        pageInfo: {
            hasNextPage: boolean;
            hasPreviousPage: boolean;
            startCursor: string;
            endCursor: string;
        };
    };
}

export interface Occasions {
    cursor: string;
    node: {
        name: string;
        code: string;
        illustrationCount: string;
        gifIllustrationCount: string;
    };
}
export interface Illustration {
    node: {
        occasion: {
            name: string;
        };
        cardImage: string;
    };
}
export interface GifIllustration {
    node: {
        occasion: {
            name: string;
        };
        gifFile: string;
    };
}
export interface GreetingsIllustrationsInterface {
    illustrations: {
        edges: Illustration[];
    };
    gifIllustrations: {
        edges: GifIllustration[];
    };
}
